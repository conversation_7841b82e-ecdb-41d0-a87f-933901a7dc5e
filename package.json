{"name": "tarot-mcp-server", "version": "1.0.0", "description": "Model Context Protocol server for Rider-Waite tarot card readings", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "dev": "tsx src/index.ts", "dev:http": "tsx src/index.ts --transport http --port 3000", "start": "node dist/index.js", "start:http": "node dist/index.js --transport http --port 3000", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts", "docker:build": "docker build -t tarot-mcp .", "docker:run": "docker run -p 3000:3000 tarot-mcp", "docker:compose": "docker-compose up -d"}, "keywords": ["mcp", "model-context-protocol", "tarot", "rider-waite", "divination", "ai"], "author": "Your Name", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^1.0.0", "zod": "^3.22.0", "express": "^4.18.0", "cors": "^2.8.5"}, "devDependencies": {"@types/node": "^20.0.0", "@types/express": "^4.17.0", "@types/cors": "^2.8.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "jest": "^29.0.0", "@types/jest": "^29.0.0", "ts-jest": "^29.0.0", "prettier": "^3.0.0", "tsx": "^4.0.0", "typescript": "^5.0.0"}, "bin": {"tarot-mcp": "./dist/index.js"}, "engines": {"node": ">=18.0.0"}}