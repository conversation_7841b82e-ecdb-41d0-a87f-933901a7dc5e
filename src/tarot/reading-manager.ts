import { TarotCardManager } from "./card-manager.js";
import { TarotSessionManager } from "./session-manager.js";
import { TarotReading, DrawnCard, CardOrientation, TarotCard } from "./types.js";
import { TAROT_SPREADS, getAllSpreads, getSpread, isValidSpreadType } from "./spreads.js";

/**
 * Manages tarot readings and interpretations
 */
export class TarotReadingManager {
  private cardManager: TarotCardManager;
  private sessionManager: TarotSessionManager;

  constructor(cardManager: TarotCardManager, sessionManager: TarotSessionManager) {
    this.cardManager = cardManager;
    this.sessionManager = sessionManager;
  }

  /**
   * Perform a tarot reading
   */
  public performReading(spreadType: string, question: string, sessionId?: string): string {
    if (!isValidSpreadType(spreadType)) {
      return `Invalid spread type: ${spreadType}. Use list_available_spreads to see valid options.`;
    }

    const spread = getSpread(spreadType)!;

    // Use cryptographically secure random card drawing
    const cards = this.cardManager.getRandomCards(spread.cardCount);

    // Generate random orientations for each card using secure randomness
    const drawnCards: DrawnCard[] = cards.map((card: any, index: number) => ({
      card,
      orientation: this.getSecureRandomOrientation(), // Cryptographically secure orientation
      position: spread.positions[index].name,
      positionMeaning: spread.positions[index].meaning
    }));

    // Create the reading
    const reading: TarotReading = {
      id: this.generateReadingId(),
      spreadType,
      question,
      cards: drawnCards,
      interpretation: this.generateInterpretation(drawnCards, question, spread.name),
      timestamp: new Date(),
      sessionId
    };

    // Add to session if provided
    if (sessionId) {
      this.sessionManager.addReadingToSession(sessionId, reading);
    }

    return this.formatReading(reading, spread.name, spread.description);
  }

  /**
   * List all available spreads
   */
  public listAvailableSpreads(): string {
    const spreads = getAllSpreads();
    
    let result = "# Available Tarot Spreads\n\n";
    
    spreads.forEach(spread => {
      result += `## ${spread.name} (${spread.cardCount} cards)\n\n`;
      result += `${spread.description}\n\n`;
      
      result += "**Positions:**\n";
      spread.positions.forEach((position, index) => {
        result += `${index + 1}. **${position.name}**: ${position.meaning}\n`;
      });
      result += "\n";
    });

    result += "Use the `perform_reading` tool with one of these spread types to get a reading.";
    
    return result;
  }

  /**
   * Interpret a combination of cards
   */
  public interpretCardCombination(cards: Array<{name: string, orientation?: string}>, context: string): string {
    const drawnCards: DrawnCard[] = [];
    
    for (const cardInput of cards) {
      const card = this.cardManager.findCard(cardInput.name);
      if (!card) {
        return `Card "${cardInput.name}" not found. Use list_all_cards to see available cards.`;
      }
      
      drawnCards.push({
        card,
        orientation: (cardInput.orientation as CardOrientation) || "upright"
      });
    }

    let result = `# Card Combination Interpretation\n\n`;
    result += `**Context:** ${context}\n\n`;
    
    result += `## Cards in This Reading\n\n`;
    drawnCards.forEach((drawnCard, index) => {
      result += `${index + 1}. **${drawnCard.card.name}** (${drawnCard.orientation})\n`;
      const keywords = drawnCard.orientation === "upright" 
        ? drawnCard.card.keywords.upright 
        : drawnCard.card.keywords.reversed;
      result += `   *Keywords: ${keywords.join(", ")}*\n\n`;
    });

    result += `## Interpretation\n\n`;
    result += this.generateCombinationInterpretation(drawnCards, context);

    return result;
  }

  /**
   * Generate interpretation for a reading
   */
  private generateInterpretation(drawnCards: DrawnCard[], question: string, spreadName: string): string {
    let interpretation = `This ${spreadName} reading addresses your question: "${question}"\n\n`;

    // Individual card interpretations with context
    drawnCards.forEach((drawnCard, index) => {
      const meanings = drawnCard.orientation === "upright"
        ? drawnCard.card.meanings.upright
        : drawnCard.card.meanings.reversed;

      interpretation += `**${drawnCard.position}**: ${drawnCard.card.name} (${drawnCard.orientation})\n`;

      // Choose the most relevant meaning based on position
      const relevantMeaning = this.selectRelevantMeaning(meanings, drawnCard.position || "General", question);
      interpretation += `${relevantMeaning}\n\n`;
    });

    // Add spread-specific analysis
    if (spreadName.toLowerCase().includes("celtic cross")) {
      interpretation += this.generateCelticCrossAnalysis(drawnCards);
    } else if (spreadName.toLowerCase().includes("three card")) {
      interpretation += this.generateThreeCardAnalysis(drawnCards);
    } else if (spreadName.toLowerCase().includes("relationship")) {
      interpretation += this.generateRelationshipAnalysis(drawnCards);
    } else if (spreadName.toLowerCase().includes("career")) {
      interpretation += this.generateCareerAnalysis(drawnCards);
    } else if (spreadName.toLowerCase().includes("spiritual")) {
      interpretation += this.generateSpiritualAnalysis(drawnCards);
    } else if (spreadName.toLowerCase().includes("chakra")) {
      interpretation += this.generateChakraAnalysis(drawnCards);
    } else if (spreadName.toLowerCase().includes("year ahead")) {
      interpretation += this.generateYearAheadAnalysis(drawnCards);
    }

    // Overall interpretation
    interpretation += this.generateOverallInterpretation(drawnCards, question);

    return interpretation;
  }

  /**
   * Select the most relevant meaning based on position and question
   */
  private selectRelevantMeaning(meanings: any, position: string, question: string): string {
    const questionLower = question.toLowerCase();
    const positionLower = position.toLowerCase();

    // Determine the most relevant aspect based on question content
    if (questionLower.includes("love") || questionLower.includes("relationship") || questionLower.includes("romance")) {
      return meanings.love;
    } else if (questionLower.includes("career") || questionLower.includes("job") || questionLower.includes("work") || questionLower.includes("money")) {
      return meanings.career;
    } else if (questionLower.includes("health") || questionLower.includes("wellness") || questionLower.includes("body")) {
      return meanings.health;
    } else if (questionLower.includes("spiritual") || questionLower.includes("purpose") || questionLower.includes("meaning")) {
      return meanings.spirituality;
    }

    // Default to general meaning, but consider position context
    if (positionLower.includes("love") || positionLower.includes("relationship")) {
      return meanings.love;
    } else if (positionLower.includes("career") || positionLower.includes("work")) {
      return meanings.career;
    }

    return meanings.general;
  }

  /**
   * Generate Celtic Cross specific analysis
   */
  private generateCelticCrossAnalysis(drawnCards: DrawnCard[]): string {
    if (drawnCards.length !== 10) return "";

    let analysis = "**Celtic Cross Analysis:**\n\n";

    // Analyze key relationships between positions
    const present = drawnCards[0];
    const challenge = drawnCards[1];
    const past = drawnCards[2];
    const future = drawnCards[3];
    const above = drawnCards[4];
    const below = drawnCards[5];
    const advice = drawnCards[6];
    const external = drawnCards[7];
    const hopesFearsCard = drawnCards[8];
    const outcome = drawnCards[9];

    // Above vs Below analysis
    analysis += `**Conscious vs Subconscious:** The ${above.card.name} above represents your conscious goals, while the ${below.card.name} below reveals your subconscious drives. `;
    if (above.orientation === below.orientation) {
      analysis += "These are aligned, suggesting harmony between your conscious desires and unconscious motivations. ";
    } else {
      analysis += "The different orientations suggest some tension between what you consciously want and what unconsciously drives you. ";
    }

    // Above vs Outcome analysis
    analysis += `**Goal vs Outcome:** Your conscious goal (${above.card.name}) `;
    if (this.cardsHaveSimilarEnergy(above, outcome)) {
      analysis += "aligns well with the likely outcome, suggesting you're on the right path. ";
    } else {
      analysis += "differs from the projected outcome, indicating you may need to adjust your approach. ";
    }

    // Future vs Outcome analysis
    analysis += `**Near Future Impact:** The ${future.card.name} in your near future will `;
    if (future.orientation === "upright") {
      analysis += "support your journey toward the final outcome. ";
    } else {
      analysis += "present challenges that need to be navigated carefully to reach your desired outcome. ";
    }

    analysis += "\n";
    return analysis;
  }

  /**
   * Generate Three Card specific analysis
   */
  private generateThreeCardAnalysis(drawnCards: DrawnCard[]): string {
    if (drawnCards.length !== 3) return "";

    let analysis = "**Three Card Flow Analysis:**\n\n";

    const [past, present, future] = drawnCards;

    analysis += `**The Journey:** From ${past.card.name} in the past, through ${present.card.name} in the present, to ${future.card.name} in the future, `;

    // Analyze the progression
    const pastEnergy = past.orientation === "upright" ? "positive" : "challenging";
    const presentEnergy = present.orientation === "upright" ? "positive" : "challenging";
    const futureEnergy = future.orientation === "upright" ? "positive" : "challenging";

    if (pastEnergy === "challenging" && presentEnergy === "positive" && futureEnergy === "positive") {
      analysis += "shows a clear progression from difficulty to resolution and success. ";
    } else if (pastEnergy === "positive" && presentEnergy === "challenging" && futureEnergy === "positive") {
      analysis += "indicates a temporary setback that will resolve positively. ";
    } else if (pastEnergy === "positive" && presentEnergy === "positive" && futureEnergy === "positive") {
      analysis += "reveals a consistently positive trajectory with continued growth. ";
    } else {
      analysis += "shows a complex journey requiring careful attention to the lessons each phase offers. ";
    }

    analysis += "\n";
    return analysis;
  }

  /**
   * Generate Relationship spread specific analysis
   */
  private generateRelationshipAnalysis(drawnCards: DrawnCard[]): string {
    if (drawnCards.length !== 7) return "";

    let analysis = "**Relationship Dynamics Analysis:**\n\n";

    const you = drawnCards[0];
    const partner = drawnCards[1];
    const relationship = drawnCards[2];
    const unites = drawnCards[3];
    const divides = drawnCards[4];

    // Analyze compatibility
    analysis += `**Compatibility Assessment:** `;
    if (you.orientation === partner.orientation) {
      analysis += "You and your partner are currently in similar emotional states, which can create harmony. ";
    } else {
      analysis += "You and your partner are in different emotional phases, which requires understanding and patience. ";
    }

    // Analyze relationship balance
    const positiveCards = [you, partner, relationship, unites].filter(c => c.orientation === "upright").length;
    if (positiveCards >= 3) {
      analysis += "The overall energy of the relationship is positive and supportive. ";
    } else {
      analysis += "The relationship may need attention and conscious effort to improve dynamics. ";
    }

    analysis += "\n";
    return analysis;
  }

  /**
   * Generate Career spread specific analysis
   */
  private generateCareerAnalysis(drawnCards: DrawnCard[]): string {
    if (drawnCards.length !== 6) return "";

    let analysis = "**Career Path Analysis:**\n\n";

    const current = drawnCards[0];
    const skills = drawnCards[1];
    const challenges = drawnCards[2];
    const opportunities = drawnCards[3];

    // Analyze career readiness
    analysis += `**Career Readiness:** `;
    if (skills.orientation === "upright" && opportunities.orientation === "upright") {
      analysis += "You have strong skills and good opportunities ahead. This is a favorable time for career advancement. ";
    } else if (challenges.orientation === "reversed") {
      analysis += "Previous obstacles are clearing, making way for new professional growth. ";
    } else {
      analysis += "Focus on developing your skills and overcoming current challenges before pursuing new opportunities. ";
    }

    analysis += "\n";
    return analysis;
  }

  /**
   * Generate Spiritual Guidance spread analysis
   */
  private generateSpiritualAnalysis(drawnCards: DrawnCard[]): string {
    if (drawnCards.length !== 6) return "";

    let analysis = "**Spiritual Development Analysis:**\n\n";

    const spiritualState = drawnCards[0];
    const lessons = drawnCards[1];
    const blocks = drawnCards[2];
    const gifts = drawnCards[3];

    // Analyze spiritual progress
    analysis += `**Spiritual Progress:** `;
    if (spiritualState.orientation === "upright") {
      analysis += "You are in a positive phase of spiritual growth and awareness. ";
    } else {
      analysis += "You may be experiencing spiritual challenges or confusion that require inner work. ";
    }

    if (blocks.orientation === "reversed") {
      analysis += "Previous spiritual blocks are dissolving, allowing for greater growth. ";
    }

    analysis += "\n";
    return analysis;
  }

  /**
   * Generate Chakra Alignment spread analysis
   */
  private generateChakraAnalysis(drawnCards: DrawnCard[]): string {
    if (drawnCards.length !== 7) return "";

    let analysis = "**Chakra Energy Analysis:**\n\n";

    const uprightChakras = drawnCards.filter(c => c.orientation === "upright").length;
    const balancePercentage = (uprightChakras / 7) * 100;

    analysis += `**Overall Energy Balance:** `;
    if (balancePercentage >= 70) {
      analysis += "Your chakras are well-balanced with strong energy flow. ";
    } else if (balancePercentage >= 50) {
      analysis += "Your energy centers have moderate balance with some areas needing attention. ";
    } else {
      analysis += "Several chakras need healing and rebalancing for optimal energy flow. ";
    }

    // Identify energy patterns
    const lowerChakras = drawnCards.slice(0, 3).filter(c => c.orientation === "upright").length;
    const upperChakras = drawnCards.slice(4, 7).filter(c => c.orientation === "upright").length;

    if (lowerChakras > upperChakras) {
      analysis += "Your grounding and physical energy centers are stronger than your spiritual centers. ";
    } else if (upperChakras > lowerChakras) {
      analysis += "Your spiritual and intuitive centers are more active than your grounding centers. ";
    }

    analysis += "\n";
    return analysis;
  }

  /**
   * Generate Year Ahead spread analysis
   */
  private generateYearAheadAnalysis(drawnCards: DrawnCard[]): string {
    if (drawnCards.length !== 13) return "";

    let analysis = "**Year Ahead Overview:**\n\n";

    const overallTheme = drawnCards[0];
    const monthlyCards = drawnCards.slice(1);

    // Analyze overall year energy
    analysis += `**Year Theme:** The ${overallTheme.card.name} sets the tone for your year, `;
    if (overallTheme.orientation === "upright") {
      analysis += "indicating a positive and growth-oriented period ahead. ";
    } else {
      analysis += "suggesting a year of inner work and overcoming challenges. ";
    }

    // Analyze seasonal patterns
    const quarters = [
      monthlyCards.slice(0, 3), // Q1: Jan-Mar
      monthlyCards.slice(3, 6), // Q2: Apr-Jun
      monthlyCards.slice(6, 9), // Q3: Jul-Sep
      monthlyCards.slice(9, 12) // Q4: Oct-Dec
    ];

    quarters.forEach((quarter, index) => {
      const uprightCount = quarter.filter(c => c.orientation === "upright").length;
      const quarterNames = ["First Quarter", "Second Quarter", "Third Quarter", "Fourth Quarter"];

      analysis += `**${quarterNames[index]}:** `;
      if (uprightCount >= 2) {
        analysis += "A positive and productive period. ";
      } else {
        analysis += "A time for patience and inner work. ";
      }
    });

    analysis += "\n";
    return analysis;
  }

  /**
   * Check if two cards have similar energy
   */
  private cardsHaveSimilarEnergy(card1: DrawnCard, card2: DrawnCard): boolean {
    // Simple heuristic: same orientation and similar themes
    if (card1.orientation !== card2.orientation) return false;

    // Check for similar suits or arcana
    if (card1.card.suit && card2.card.suit && card1.card.suit === card2.card.suit) return true;
    if (card1.card.arcana === card2.card.arcana) return true;

    return false;
  }

  /**
   * Generate overall interpretation considering card interactions
   */
  private generateOverallInterpretation(drawnCards: DrawnCard[], question: string): string {
    let overall = "**Overall Interpretation:**\n\n";

    // Analyze the energy of the reading
    const uprightCount = drawnCards.filter(c => c.orientation === "upright").length;
    const reversedCount = drawnCards.filter(c => c.orientation === "reversed").length;
    const majorArcanaCount = drawnCards.filter(c => c.card.arcana === "major").length;
    const totalCards = drawnCards.length;

    // Major Arcana influence analysis
    if (majorArcanaCount > totalCards / 2) {
      overall += "This reading is heavily influenced by Major Arcana cards, indicating that significant spiritual forces, life lessons, and karmic influences are at work. The universe is guiding you through important transformations. ";
    } else if (majorArcanaCount === 0) {
      overall += "This reading contains only Minor Arcana cards, suggesting that the situation is primarily within your control and relates to everyday matters and practical concerns. ";
    } else {
      overall += "The balance of Major and Minor Arcana cards suggests a blend of spiritual guidance and practical action is needed. ";
    }

    // Orientation analysis
    const uprightPercentage = (uprightCount / totalCards) * 100;
    if (uprightPercentage >= 80) {
      overall += "The predominance of upright cards indicates positive energy, clear direction, and favorable circumstances. You're aligned with the natural flow of events. ";
    } else if (uprightPercentage >= 60) {
      overall += "Most cards are upright, suggesting generally positive energy with some areas requiring attention or inner work. ";
    } else if (uprightPercentage >= 40) {
      overall += "The balance of upright and reversed cards indicates a mixed situation with both opportunities and challenges present. ";
    } else if (uprightPercentage >= 20) {
      overall += "The majority of reversed cards suggests internal blocks, delays, or the need for significant introspection and inner work. ";
    } else {
      overall += "The predominance of reversed cards indicates a time of deep inner transformation, spiritual crisis, or significant obstacles that require patience and self-reflection. ";
    }

    // Add specific guidance based on card combinations and spread type
    overall += this.generateAdvancedCombinationInterpretation(drawnCards, question);

    return overall;
  }

  /**
   * Generate advanced interpretation for card combinations
   */
  private generateAdvancedCombinationInterpretation(drawnCards: DrawnCard[], context: string): string {
    let interpretation = "";

    // Elemental analysis
    const elementCounts = this.analyzeElements(drawnCards);
    interpretation += this.interpretElementalBalance(elementCounts);

    // Suit analysis for Minor Arcana
    const suitAnalysis = this.analyzeSuits(drawnCards);
    interpretation += suitAnalysis;

    // Numerical patterns
    const numericalAnalysis = this.analyzeNumericalPatterns(drawnCards);
    interpretation += numericalAnalysis;

    // Court card analysis
    const courtCardAnalysis = this.analyzeCourtCards(drawnCards);
    interpretation += courtCardAnalysis;

    // Archetypal patterns in Major Arcana
    const archetypeAnalysis = this.analyzeMajorArcanaPatterns(drawnCards);
    interpretation += archetypeAnalysis;

    interpretation += "\n\nTrust your intuition as you reflect on these insights and how they apply to your specific situation.";

    return interpretation;
  }

  /**
   * Analyze elemental balance in the reading
   */
  private analyzeElements(drawnCards: DrawnCard[]): Record<string, number> {
    const elementCounts = { fire: 0, water: 0, air: 0, earth: 0 };

    drawnCards.forEach(drawnCard => {
      if (drawnCard.card.element) {
        elementCounts[drawnCard.card.element]++;
      }
    });

    return elementCounts;
  }

  /**
   * Interpret elemental balance
   */
  private interpretElementalBalance(elementCounts: Record<string, number>): string {
    const total = Object.values(elementCounts).reduce((a, b) => a + b, 0);
    if (total === 0) return "";

    let interpretation = "";
    const dominantElement = Object.entries(elementCounts)
      .sort(([,a], [,b]) => b - a)[0];

    if (dominantElement[1] > total / 2) {
      switch (dominantElement[0]) {
        case "fire":
          interpretation += "The dominance of Fire energy suggests this is a time for action, creativity, and passionate pursuit of your goals. ";
          break;
        case "water":
          interpretation += "The prevalence of Water energy indicates this situation is deeply emotional and intuitive, requiring you to trust your feelings. ";
          break;
        case "air":
          interpretation += "The abundance of Air energy suggests this is primarily a mental matter requiring clear thinking, communication, and intellectual approach. ";
          break;
        case "earth":
          interpretation += "The strong Earth energy indicates this situation requires practical action, patience, and attention to material concerns. ";
          break;
      }
    }

    // Check for missing elements
    const missingElements = Object.entries(elementCounts)
      .filter(([, count]) => count === 0)
      .map(([element]) => element);

    if (missingElements.length > 0) {
      interpretation += `The absence of ${missingElements.join(" and ")} energy suggests you may need to cultivate these qualities to achieve balance. `;
    }

    return interpretation;
  }

  /**
   * Analyze suit patterns
   */
  private analyzeSuits(drawnCards: DrawnCard[]): string {
    const suits = drawnCards
      .filter(c => c.card.suit)
      .map(c => c.card.suit!);

    const suitCounts = suits.reduce((acc, suit) => {
      acc[suit] = (acc[suit] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const dominantSuit = Object.entries(suitCounts)
      .sort(([,a], [,b]) => b - a)[0];

    if (!dominantSuit || dominantSuit[1] <= 1) return "";

    let interpretation = "";
    switch (dominantSuit[0]) {
      case "wands":
        interpretation += "The multiple Wands indicate this situation involves creative projects, career ambitions, and the need for decisive action. ";
        break;
      case "cups":
        interpretation += "The presence of multiple Cups shows this is fundamentally about emotions, relationships, and spiritual matters. ";
        break;
      case "swords":
        interpretation += "The dominance of Swords reveals this situation involves mental challenges, conflicts, and the need for clear communication. ";
        break;
      case "pentacles":
        interpretation += "Multiple Pentacles emphasize material concerns, financial matters, and the need for practical, grounded action. ";
        break;
    }

    return interpretation;
  }

  /**
   * Analyze numerical patterns in the reading
   */
  private analyzeNumericalPatterns(drawnCards: DrawnCard[]): string {
    const numbers = drawnCards
      .filter(c => c.card.number !== undefined)
      .map(c => c.card.number!);

    if (numbers.length < 2) return "";

    let interpretation = "";
    const avgNumber = numbers.reduce((a, b) => a + b, 0) / numbers.length;

    // Analyze the journey stage
    if (avgNumber <= 3) {
      interpretation += "The low-numbered cards indicate this situation is in its beginning stages, full of potential and new energy. ";
    } else if (avgNumber <= 6) {
      interpretation += "The mid-range numbers suggest this situation is in its development phase, requiring steady progress and patience. ";
    } else if (avgNumber <= 9) {
      interpretation += "The higher numbers indicate this situation is approaching completion or mastery, requiring final efforts. ";
    } else {
      interpretation += "The presence of high numbers and court cards suggests mastery, completion, or the involvement of significant people. ";
    }

    // Look for repeated numbers
    const numberCounts = numbers.reduce((acc, num) => {
      acc[num] = (acc[num] || 0) + 1;
      return acc;
    }, {} as Record<number, number>);

    const repeatedNumbers = Object.entries(numberCounts)
      .filter(([, count]) => count > 1)
      .map(([num]) => parseInt(num));

    if (repeatedNumbers.length > 0) {
      interpretation += `The repetition of ${repeatedNumbers.join(" and ")} emphasizes the themes of `;
      repeatedNumbers.forEach(num => {
        switch (num) {
          case 1: interpretation += "new beginnings and potential, "; break;
          case 2: interpretation += "balance and partnerships, "; break;
          case 3: interpretation += "creativity and growth, "; break;
          case 4: interpretation += "stability and foundation, "; break;
          case 5: interpretation += "change and challenge, "; break;
          case 6: interpretation += "harmony and responsibility, "; break;
          case 7: interpretation += "spiritual development and introspection, "; break;
          case 8: interpretation += "material mastery and achievement, "; break;
          case 9: interpretation += "completion and wisdom, "; break;
          case 10: interpretation += "fulfillment and new cycles, "; break;
        }
      });
      interpretation = interpretation.slice(0, -2) + ". ";
    }

    return interpretation;
  }

  /**
   * Analyze court cards in the reading
   */
  private analyzeCourtCards(drawnCards: DrawnCard[]): string {
    const courtCards = drawnCards.filter(c =>
      c.card.name.includes("Page") ||
      c.card.name.includes("Knight") ||
      c.card.name.includes("Queen") ||
      c.card.name.includes("King")
    );

    if (courtCards.length === 0) return "";

    let interpretation = "";
    if (courtCards.length === 1) {
      interpretation += "The presence of a court card suggests that a specific person or personality aspect is significant to this situation. ";
    } else {
      interpretation += `The ${courtCards.length} court cards indicate that multiple people or personality aspects are influencing this situation. `;
    }

    return interpretation;
  }

  /**
   * Analyze Major Arcana patterns and archetypal themes
   */
  private analyzeMajorArcanaPatterns(drawnCards: DrawnCard[]): string {
    const majorCards = drawnCards.filter(c => c.card.arcana === "major");
    if (majorCards.length === 0) return "";

    let interpretation = "";

    // Analyze the Fool's Journey progression
    const majorNumbers = majorCards
      .map(c => c.card.number!)
      .sort((a, b) => a - b);

    if (majorNumbers.length > 1) {
      const span = majorNumbers[majorNumbers.length - 1] - majorNumbers[0];
      if (span > 10) {
        interpretation += "The wide span of Major Arcana cards suggests you're experiencing a significant life transformation that touches many aspects of your spiritual journey. ";
      } else if (span < 5) {
        interpretation += "The close grouping of Major Arcana cards indicates you're working through a specific phase of spiritual development. ";
      }
    }

    // Look for specific archetypal themes
    const cardNames = majorCards.map(c => c.card.name.toLowerCase());

    if (cardNames.includes("the fool") && cardNames.includes("the magician")) {
      interpretation += "The presence of both The Fool and The Magician suggests a powerful combination of new beginnings and the ability to manifest your desires. ";
    }

    if (cardNames.includes("the high priestess") && cardNames.includes("the hierophant")) {
      interpretation += "The High Priestess and Hierophant together indicate a balance between inner wisdom and traditional teachings. ";
    }

    return interpretation;
  }

  /**
   * Generate interpretation for card combinations (legacy method for compatibility)
   */
  private generateCombinationInterpretation(drawnCards: DrawnCard[], context: string): string {
    return this.generateAdvancedCombinationInterpretation(drawnCards, context);
  }

  /**
   * Format a reading for display
   */
  private formatReading(reading: TarotReading, spreadName: string, spreadDescription: string): string {
    let result = `# ${spreadName} Reading\n\n`;
    result += `**Question:** ${reading.question}\n`;
    result += `**Date:** ${reading.timestamp.toLocaleString()}\n`;
    result += `**Reading ID:** ${reading.id}\n\n`;
    
    result += `*${spreadDescription}*\n\n`;
    
    result += `## Your Cards\n\n`;
    reading.cards.forEach((drawnCard, index) => {
      result += `### ${index + 1}. ${drawnCard.position}\n`;
      if (drawnCard.positionMeaning) {
        result += `*${drawnCard.positionMeaning}*\n\n`;
      }
      result += `**${drawnCard.card.name}** (${drawnCard.orientation})\n\n`;
      
      const keywords = drawnCard.orientation === "upright" 
        ? drawnCard.card.keywords.upright 
        : drawnCard.card.keywords.reversed;
      result += `*Keywords: ${keywords.join(", ")}*\n\n`;
    });

    result += `## Interpretation\n\n`;
    result += reading.interpretation;

    return result;
  }

  /**
   * Get a specific spread by type
   */
  public getSpreadByType(spreadType: string): any {
    return getSpread(spreadType);
  }

  /**
   * Generate cryptographically secure random orientation
   */
  private getSecureRandomOrientation(): CardOrientation {
    // Use the same secure random method as card manager
    const random = this.getSecureRandom();
    return random < 0.5 ? "upright" : "reversed"; // 50% chance upright, 50% reversed
  }

  /**
   * Generate cryptographically secure random number
   */
  private getSecureRandom(): number {
    if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
      // Browser environment with Web Crypto API
      const array = new Uint32Array(1);
      crypto.getRandomValues(array);
      return array[0] / (0xffffffff + 1);
    } else if (typeof require !== 'undefined') {
      // Node.js environment
      try {
        const crypto = require('crypto');
        return crypto.randomBytes(4).readUInt32BE(0) / (0xffffffff + 1);
      } catch (e) {
        // Fallback to Math.random if crypto is not available
        console.warn('Crypto module not available, falling back to Math.random()');
        return Math.random();
      }
    } else {
      // Fallback to Math.random
      return Math.random();
    }
  }

  /**
   * Generate a unique reading ID with secure randomness
   */
  private generateReadingId(): string {
    const timestamp = Date.now();
    const randomPart = Math.floor(this.getSecureRandom() * 1000000000).toString(36);
    return `reading_${timestamp}_${randomPart}`;
  }
}
